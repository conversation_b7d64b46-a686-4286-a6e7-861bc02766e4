{"name": "pinnacle-core-portal", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "type-check": "tsc --noEmit"}, "dependencies": {"next": "^14.2.0", "react": "^18.3.0", "react-dom": "^18.3.0", "@next-auth/prisma-adapter": "^1.0.7", "next-auth": "^4.24.0", "@prisma/client": "^5.15.0", "prisma": "^5.15.0", "socket.io": "^4.7.5", "socket.io-client": "^4.7.5", "mssql": "^10.0.2", "bcryptjs": "^2.4.3", "zod": "^3.23.0", "react-hook-form": "^7.51.0", "@hookform/resolvers": "^3.3.0", "lucide-react": "^0.379.0", "clsx": "^2.1.0", "tailwind-merge": "^2.3.0", "class-variance-authority": "^0.7.0"}, "devDependencies": {"typescript": "^5.4.0", "@types/node": "^20.12.0", "@types/react": "^18.3.0", "@types/react-dom": "^18.3.0", "@types/bcryptjs": "^2.4.6", "eslint": "^8.57.0", "eslint-config-next": "^14.2.0", "tailwindcss": "^3.4.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "@types/mssql": "^9.1.5"}}