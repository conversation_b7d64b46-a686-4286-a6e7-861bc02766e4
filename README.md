# Pinnacle-Core-Portal

Pinnacle-Core-Portal is a comprehensive platform designed to streamline operations with a focus on eInvoicing middleware capabilities. It features a modular architecture, real-time notifications, customizable dashboards, and robust user management, built with a modern technology stack for scalability and maintainability.

---

## ✨ Key Features

* **Modular Authentication:** Secure login/signup using NextAuth.js with support for credentials and third-party OAuth providers.
* **Multi-Tenancy Support:** Country and Company selection post-login to tailor user experience and data access.
* **Customizable Dashboards:** Role-based dashboard customization allowing admins to design layouts, sections, and widgets.
* **User Management:** Company-level administration for managing users, roles, and permissions.
* **eInvoicing Middleware:** Core functionality for eInvoicing processes, integrated with the portal.
* **Real-Time Notifications:** In-app notifications via Socket.io for important events and updates.
* **User Activity Logging:** Tracking and display of user-relevant activities within the portal.
* **Dual Database Strategy:** PostgreSQL for primary application data and MSSQL for detailed internal logging.
* **Sandbox/Production Context:** Ability to select company context (e.g., production vs. sandbox) for data isolation.

---

## 🛠️ Technology Stack

* **Frontend:** Next.js 13+ (App Router), React, Tailwind CSS
* **Backend/API:** Next.js API Routes (Route Handlers)
* **Authentication:** NextAuth.js v4/v5
* **ORM:** Prisma
* **Databases:**
    * **Primary (Global):** PostgreSQL
    * **Internal Logs:** Microsoft SQL Server (MSSQL)
* **Real-time Communication:** Socket.io
* **Language:** TypeScript

---

## 🚀 Getting Started

These instructions will get you a copy of the project up and running on your local machine for development and testing purposes.

### Prerequisites

* Node.js (v18.x or later recommended)
* npm, yarn, or pnpm
* PostgreSQL server installed and running
* MSSQL server installed and running (optional for basic development, required for full logging features)
* Git

### Installation & Setup

1.  **Clone the repository:**
    ```bash
    git clone <your-repository-url>
    cd pinnacle-core-portal
    ```

2.  **Install dependencies:**
    ```bash
    npm install
    # or
    yarn install
    # or
    pnpm install
    ```

3.  **Set up environment variables:**
    Create a `.env.local` file in the root of the project by copying the example file:
    ```bash
    cp .env.example .env.local
    ```
    Update `.env.local` with your specific configuration:
    ```env
    # PostgreSQL Connection String
    DATABASE_URL="postgresql://USER:PASSWORD@HOST:PORT/DATABASE?schema=public"

    # MSSQL Connection String (for internal logs)
    # Example: "sqlserver://HOST:PORT;database=DATABASE;user=USER;password=PASSWORD;encrypt=true;trustServerCertificate=true;"
    MSSQL_LOG_DATABASE_URL="your_mssql_connection_string"

    # NextAuth Configuration
    NEXTAUTH_URL="http://localhost:3000" # Your application's base URL
    NEXTAUTH_SECRET="your_strong_random_secret_here" # Generate a strong secret

    # Google OAuth (Example - if you add Google Provider)
    # GOOGLE_CLIENT_ID=""
    # GOOGLE_CLIENT_SECRET=""

    # Application specific variables (if any)
    # NEXT_PUBLIC_SOCKET_URL="http://localhost:3001" # If socket server is separate

    # Add other necessary environment variables as the project grows
    ```
    **Note:** `NEXTAUTH_SECRET` can be generated using `openssl rand -base64 32` in your terminal.

4.  **Set up the database schema (PostgreSQL):**
    Ensure your PostgreSQL server is running and the database specified in `DATABASE_URL` exists. Then, run Prisma migrations:
    ```bash
    npx prisma migrate dev --name init
    ```
    This will apply the schema defined in `prisma/schema.prisma` to your PostgreSQL database.

5.  **Run the development server:**
    ```bash
    npm run dev
    # or
    yarn dev
    # or
    pnpm dev
    ```
    The application should now be running at `http://localhost:3000`.

---

## 📁 Project Structure

The project follows a feature-based and domain-driven directory structure for maintainability using the Next.js App Router.


pinnacle-core-portal/
├── public/                     # Static assets (images, fonts, etc.)
├── src/
│   ├── app/                    # Next.js App Router (pages, layouts, route handlers)
│   │   ├── (auth)/             # Auth-related routes (login, signup)
│   │   ├── (main)/             # Main application routes post-login
│   │   │   ├── layout.tsx      # Main app layout (nav, sidebar)
│   │   │   └── (pages)/        # dashboard, select-country, notifications, etc.
│   │   └── api/                # API Route Handlers (backend logic)
│   ├── components/             # Shared UI components
│   │   ├── ui/                 # Generic UI elements (Button, Input, Modal)
│   │   └── layout/             # Layout structure components (Navbar, Sidebar, NotificationBell)
│   ├── features/               # Feature-specific UI components and hooks
│   ├── lib/                    # Core utilities, helpers, clients (Prisma, NextAuth, MSSQL, Socket.io)
│   ├── services/               # Backend service logic (e.g., NotificationService, EinvoiceService)
│   ├── prisma/                 # Prisma schema, migrations, seeds
│   │   ├── schema.prisma       # Defines PostgreSQL schema
│   │   └── migrations/
│   ├── styles/                 # Global styles, Tailwind CSS config
│   └── types/                  # TypeScript type definitions
├── .env.local                  # Local environment variables (Gitignored)
├── .env.example                # Example environment file
├── next.config.js              # Next.js configuration
├── tailwind.config.js          # Tailwind CSS configuration
├── tsconfig.json               # TypeScript configuration
└── package.json                # Project dependencies and scripts


---

## 🔐 Authentication

Authentication is handled by NextAuth.js.

* **Configuration:** `src/lib/authOptions.ts` defines providers (Credentials, OAuth) and callbacks. The main route handler is at `src/app/api/auth/[...nextauth]/route.ts`.
* **Providers:**
    * **Credentials:** Standard email/password login.
    * **OAuth:** Easily extendable for third-party providers (e.g., Google, Microsoft) to enable consuming sessions from other services.
* **Session Management:** JWT (JSON Web Tokens) are used for session strategy.
* **Protected Routes:** Middleware (`src/middleware.ts`) can be used to protect routes based on authentication status and roles.

---

## 🗂️ Database Strategy

The application employs a dual-database strategy:

* **PostgreSQL (Global - Managed by Prisma):**
    * **Purpose:** The primary datastore for all core application data including users, companies, eInvoices, dashboard configurations, notifications, and user-facing activity logs.
    * **Schema:** Defined in `prisma/schema.prisma`. Key models include `User`, `Company`, `Country`, `Einvoice`, `Notification`, `UserActivityLog`, `UserPreference`, `UserRole` (enum), `NotificationType` (enum), etc.
    * **Migrations:** Managed by Prisma Migrate (`npx prisma migrate dev`).
    * **Client:** Instantiated in `src/lib/prisma.ts`.
    * **Sandbox/Production Context:**
        * The system supports company selection that can differentiate between environments (e.g., "Company A - Sandbox" vs. "Company A - Production").
        * This can be achieved via:
            1.  **Logical Separation:** Flags within the same database (e.g., an `environment` field on company-specific data).
            2.  **Physical Separation:** Pointing to different database instances/schemas (requires dynamic connection handling in the application layer, potentially storing connection strings or identifiers in the `Company` model).

* **Microsoft SQL Server (MSSQL - Internal Logs):**
    * **Purpose:** Dedicated to storing detailed, verbose internal system logs, comprehensive audit trails, and operational metrics not typically exposed directly to end-users.
    * **Integration:** Accessed via a direct MSSQL driver (e.g., `tedious` or `mssql` library). A client wrapper might be in `src/lib/mssql.ts`. Prisma *can* be used for MSSQL but is often simpler to use a direct driver for pure logging scenarios.

---

## ⚙️ Core Feature Implementation Overview

1.  **User Onboarding & Login:** Secure sign-up and login via NextAuth.
2.  **Context Selection:** Post-login, users select their operational Country and Company (including environment like Sandbox/Production if configured). The UI for company selection will be similar to the image provided by the user (listing name, status, subscription end date).
3.  **Dashboard:**
    * Displays key information and widgets relevant to the user's role and selected company.
    * Administrators (Portal/Company, based on `UserRole`) can customize dashboard layouts, content, and widgets.
4.  **User Management:** Company Admins (`UserRole.COMPANY_ADMIN`) manage users, assign roles (potentially more granular roles within `UserCompanyAccess`), and control access within their company.
5.  **eInvoicing:** Core APIs and UI for managing eInvoicing lifecycle.
6.  **Notifications:** Real-time updates (via Socket.io) for important events, viewable in a dedicated section (`/notifications`) and through UI cues (e.g., a notification bell in the main layout).
7.  **Activity Logs:** A user-facing log (`/activity-log`) of significant actions and transactions within their scope, sourced from the `UserActivityLog` table.

---

## 🌐 API Endpoints

API logic is implemented using Next.js Route Handlers in the `src/app/api/` directory. Key endpoint groups include:

* `/api/auth/*`: Handled by NextAuth.js for authentication.
* `/api/countries`: For fetching country data.
* `/api/companies`: For fetching company data related to the user.
* `/api/dashboard`: For fetching and updating dashboard configurations.
* `/api/users`: For user management operations by admins.
* `/api/einvoicing/*`: For eInvoicing operations.
* `/api/notifications`: For managing notifications (e.g., fetching, marking as read).
* `/api/activity`: For fetching user activity logs.

*(Specific endpoint definitions and request/response schemas should be documented further, potentially using tools like Swagger/OpenAPI if extensive API documentation is needed).*

---

## 🔧 Maintainability & Scalability Considerations

* **TypeScript:** Enforced for type safety and improved developer experience.
* **Environment Variables:** Centralized configuration management.
* **Testing:** Commitment to unit, integration, and end-to-end tests (tools like Jest, Vitest, Playwright/Cypress).
* **Linting & Formatting:** ESLint and Prettier for code consistency.
* **CI/CD:** Automated pipelines for testing and deployment.
* **Logging:** Comprehensive logging using PostgreSQL (`UserActivityLog`) for user-facing events and MSSQL for internal system diagnostics.
* **Error Handling:** Robust error handling and reporting mechanisms (e.g., Sentry).
* **Scalability:**
    * Leverage Next.js serverless functions for API scaling.
    * Optimize database performance (indexing, connection pooling).
    * Implement caching where appropriate.
    * Scale Socket.io infrastructure if real-time load increases significantly (e.g., using Redis adapter for Socket.io across multiple instances).

---

## 🤝 Contributing (Optional)

If this is an open-source or team project, add guidelines here:

* Fork the repository.
* Create your feature branch (`git checkout -b feature/AmazingFeature`).
* Commit your changes (`git commit -m 'Add some AmazingFeature'`).
* Push to the branch (`git push origin feature/AmazingFeature`).
* Open a Pull Request.

---

## 📄 License (Optional)

Specify the project license here (e.g., MIT, Apache 2.0).

